import { config } from '@/theme/_config';
import React, { useRef, useEffect } from 'react';
import { View, FlatList, Animated } from 'react-native';
import { ScaledSheet } from 'react-native-size-matters';
import Typography from '../Typography/Typography';
import { Fonts } from '@/constants';

// Define the Tip type
type Tip = {
  id: string;
  title: string;
  description: string;
};

const TIPS: Tip[] = [
  {
    id: '1',
    title: 'Take Clear Photos for the Best Results',
    description:
      'Make sure all ingredients are visible — avoid covering them with sauces or toppings.',
  },
  {
    id: '2',
    title: 'Personalized Suggestions',
    description:
      "Ingredients you've logged before are more likely to be recognized, as the AI adapts to your preferences.",
  },
  {
    id: '3',
    title: 'Tailored for PKU',
    description:
      'The AI prioritizes low-protein or special dietary items when relevant.',
  },
  {
    id: '4',
    title: 'Please Note',
    description:
      "It's harder for the AI to estimate dishes with mixed ingredients than those with clearly separated items.",
  },
];

interface CameraTipsCarouselProps {
  style?: any;
}

export const CameraTipsCarousel: React.FC<CameraTipsCarouselProps> = (props) => {
  const scrollX = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList<Tip>>(null);
  const currentIndex = useRef(0);

  useEffect(() => {
    const interval = setInterval(() => {
      currentIndex.current = (currentIndex.current + 1) % TIPS.length;
      flatListRef.current?.scrollToIndex({
        index: currentIndex.current,
        animated: true,
      });
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <View style={[styles.container, props.style]}>
      <View style={styles.indicatorContainer}>
        {TIPS.map((_, index) => {
          // Calculate the actual item width: card width (280) + horizontal margins (48 * 2)
          const itemWidth = 280 + (48 * 2);
          const inputRange = [
            (index - 1) * itemWidth,
            index * itemWidth,
            (index + 1) * itemWidth,
          ];

          const opacity = scrollX.interpolate({
            inputRange,
            outputRange: [0.4, 1, 0.4],
            extrapolate: 'clamp',
          });

          const scale = scrollX.interpolate({
            inputRange,
            outputRange: [0.8, 1, 0.8],
            extrapolate: 'clamp',
          });

          const backgroundColor = scrollX.interpolate({
            inputRange,
            outputRange: [config.colors.gray, config.colors.primary, config.colors.gray],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={index.toString()}
              style={[
                styles.dot,
                {
                  backgroundColor,
                  opacity,
                  transform: [{ scale }],
                }
              ]}
            />
          );
        })}
      </View>
      <Animated.FlatList
        ref={flatListRef}
        data={TIPS}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false },
        )}
        scrollEventThrottle={16}
        renderItem={({ item }) => (
          <View style={styles.tipCard}>
            <Typography.B3 style={styles.tipTitle}>{item.title}</Typography.B3>
            <Typography.B3 style={styles.tipDescription}>{item.description}</Typography.B3>
          </View>
        )}
      />

    </View>
  );
};

const styles = ScaledSheet.create({
  container: {
    height: 120,
    alignItems: 'center',
    justifyContent: 'center'
  },
  tipCard: {
    width: '280@ms',
    marginHorizontal: '48@ms',
    padding: '16@ms',
    backgroundColor: '#F5F5F5',
    borderRadius: '20@ms',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipTitle: {
    marginBottom: 6,
    fontFamily: Fonts.RALEWAY_BOLD,
    color: '#333',
  },
  tipDescription: {
    color: '#666',
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: '10@ms',
  },
  dot: {
    height: '10@ms',
    width: '10@ms',
    borderRadius: '5@ms',
    marginHorizontal: '4@ms',
  },
});